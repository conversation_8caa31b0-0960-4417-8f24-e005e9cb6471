import React, { useState, useMemo } from "react";
import {
  <PERSON>ton,
  Grid,
  InputAdornment,
  SelectChangeEvent,
  Tabs,
  Tab,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogContentText,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
  Box,
} from "@mui/material";
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Person as PersonIcon,
  AddCircle as AddCircleIcon,
} from "@mui/icons-material";

interface Client {
  id: number;
  propietarioNombre?: string;
  propietarioDocumento?: string;
  propietarioTelefono?: string;
  propietarioEmail?: string;
  razonSocial: string;
  tipoCliente?: string;
  direccion: string;
  empresaLocalidad?: string;
  provincia?: string;
  condFrenteIva: string;
  nombreContacto?: string;
  cargoContacto?: string;
  telefono: string;
  mail: string;
  lugar: string;
  documento: string;
}

interface AgricultorModalProps {
  open: boolean;
  onClose: (event?: React.MouseEvent<HTMLElement>, reason?: string) => void;
  onSubmit: (formData: any) => void;
  estadoModal: "add" | "update";
  initialData?: Client | null;
}

const AgricultorModal: React.FC<AgricultorModalProps> = ({
  open,
  onClose,
  onSubmit,
  estadoModal,
  initialData,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [enabledTabs, setEnabledTabs] = useState<boolean[]>([
    true,
    false,
    false,
  ]);
  const [validationErrors, setValidationErrors] = useState<{
    [key: string]: string;
  }>({});

  const [formData, setFormData] = useState({
    propietarioNombre: initialData?.propietarioNombre || "",
    propietarioDocumento: initialData?.propietarioDocumento || "",
    propietarioTelefono: initialData?.propietarioTelefono || "",
    propietarioEmail: initialData?.propietarioEmail || "",
    usarComoRazonSocial: false,
    empresaRazonSocial: initialData?.razonSocial || "",
    empresaTipoCliente: initialData?.tipoCliente || "",
    empresaDomicilio: initialData?.direccion || "",
    empresaLocalidad: initialData?.empresaLocalidad || "",
    empresaProvincia: initialData?.provincia || "",
    empresaCondFrenteIva: initialData?.condFrenteIva || "",
    contactos: [
      {
        id: 1,
        nombre: initialData?.nombreContacto || "",
        cargo: initialData?.cargoContacto || "",
        telefono: initialData?.telefono || "",
        email: initialData?.mail || "",
      },
    ],
  });

  const provincias = useMemo(
    () => [
      "Buenos Aires",
      "Catamarca",
      "Chaco",
      "Chubut",
      "Córdoba",
      "Corrientes",
      "Entre Ríos",
      "Formosa",
      "Jujuy",
      "La Pampa",
      "La Rioja",
      "Mendoza",
      "Misiones",
      "Neuquén",
      "Río Negro",
      "Salta",
      "San Juan",
      "San Luis",
      "Santa Cruz",
      "Santa Fe",
      "Santiago del Estero",
      "Tierra del Fuego",
      "Tucumán",
    ],
    []
  );

  const tipoClienteOptions = useMemo(
    () => [
      { value: "Productor(comercial)", category: "Productores" },
      { value: "Productor(familiar)", category: "Productores" },
      { value: "Estancia", category: "Productores" },
      {
        value: "Empresa (persona jurídica, p. ej. SA / SRL)",
        category: "Empresas",
      },
      { value: "Cooperativa", category: "Empresas" },
      { value: "Asociación/Consorcio/Entidad Gremial", category: "Empresas" },
      {
        value: "Contratista(p. ej. otro que contrata equipo)",
        category: "Servicios",
      },
      {
        value: "Acopio/Industria/Exportador(silos, plantas, compradoras)",
        category: "Servicios",
      },
      { value: "Municipalidad/Estatal/Gubernamental", category: "Público" },
      { value: "Particular(pequeños clientes domésticos)", category: "Otros" },
      { value: "Otro(para casos no previstos)", category: "Otros" },
      { value: "No Especificado", category: "Otros" },
    ],
    []
  );

  const condFrenteIvaOptions = useMemo(
    () => [
      "IVA Responsable Inscripto",
      "IVA Responsable no Inscripto",
      "IVA no Responsable",
      "IVA Sujeto Exento",
      "Consumidor Final",
      "Responsable Monotributo",
      "Sujeto no Categorizado",
      "Proveedor del Exterior",
      "Cliente del Exterior",
      "IVA Liberado",
      "Pequeño Contribuyente Social",
      "Monotributista Social",
      "Pequeño Contribuyente Eventual",
    ],
    []
  );

  const labelStyles = useMemo(
    () => ({
      fontWeight: 600,
      color: "#333",
      marginBottom: "8px",
      display: "block",
      fontFamily: "Lexend, sans-serif",
    }),
    []
  );

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;

    // Validaciones para campos de texto (nombres, razón social, etc.)
    if (
      name === "propietarioNombre" ||
      name === "empresaRazonSocial" ||
      name === "empresaLocalidad" ||
      (name.startsWith("contacto") && name.includes("nombre"))
    ) {
      if (!/^[a-zA-ZÀ-ÿ\s]*$/.test(value)) {
        setValidationErrors((prevError) => ({
          ...prevError,
          [name]: "Solo se permiten letras y espacios",
        }));
        return;
      } else {
        setValidationErrors((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    // Validaciones para domicilio
    if (name === "empresaDomicilio") {
      if (!/^[a-zA-ZÀ-ÿ0-9\s.]*$/.test(value)) {
        setValidationErrors((prevError) => ({
          ...prevError,
          [name]: "Solo se permiten letras, números, espacios y puntos",
        }));
        return;
      } else {
        setValidationErrors((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    // Validaciones para teléfonos
    if (name === "propietarioTelefono" || name.includes("telefono")) {
      const cleaned = value.replace(/\D/g, "");
      if (cleaned.length > 10) return;

      let formatted: string;
      if (cleaned.length <= 4) {
        formatted = cleaned;
      } else {
        formatted = `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`;
      }

      const isValidFormat = /^\d{4}-\d{6}$/.test(formatted);
      setValidationErrors((prevError) => ({
        ...prevError,
        [name]:
          formatted.length === 11 && !isValidFormat
            ? "Formato inválido. Debe ser 0000-000000"
            : "",
      }));

      setFormData((prevState) => ({
        ...prevState,
        [name]: formatted,
      }));
      return;
    }

    // Validaciones para documento
    if (name === "propietarioDocumento") {
      const cleaned = value.replace(/\D/g, "");
      if (cleaned.length > 11) return;

      let formatted: string;
      if (cleaned.length <= 2) {
        formatted = cleaned;
      } else if (cleaned.length <= 10) {
        formatted = `${cleaned.slice(0, 2)}-${cleaned.slice(2)}`;
      } else {
        formatted = `${cleaned.slice(0, 2)}-${cleaned.slice(
          2,
          10
        )}-${cleaned.slice(10, 11)}`;
      }

      const isValidFormat = /^\d{2}-\d{8}-\d{1}$/.test(formatted);
      setValidationErrors((prevError) => ({
        ...prevError,
        [name]:
          formatted.length === 12 && !isValidFormat
            ? "Formato inválido. Debe ser 00-00000000-0"
            : "",
      }));

      setFormData((prevState) => ({
        ...prevState,
        [name]: formatted,
      }));
      return;
    }

    setFormData((prevState) => {
      const newState = {
        ...prevState,
        [name]: value,
      };

      // Si está marcada la opción de usar como razón social, sincronizar datos
      if (prevState.usarComoRazonSocial) {
        if (name === "propietarioNombre") {
          newState.empresaRazonSocial = value;
          newState.contactos = [
            {
              ...prevState.contactos[0],
              nombre: value,
            },
            ...prevState.contactos.slice(1),
          ];
        } else if (name === "propietarioTelefono") {
          newState.contactos = [
            {
              ...prevState.contactos[0],
              telefono: value,
            },
            ...prevState.contactos.slice(1),
          ];
        } else if (name === "propietarioEmail") {
          newState.contactos = [
            {
              ...prevState.contactos[0],
              email: value,
            },
            ...prevState.contactos.slice(1),
          ];
        }
      }

      return newState;
    });
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Funciones de validación para cada pestaña
  const validateTab1 = (): boolean => {
    const {
      propietarioNombre,
      propietarioDocumento,
      propietarioTelefono,
      propietarioEmail,
    } = formData;
    return !!(
      propietarioNombre.trim() &&
      propietarioDocumento.trim() &&
      propietarioTelefono.trim() &&
      propietarioEmail.trim()
    );
  };

  const validateTab2 = (): boolean => {
    const {
      empresaRazonSocial,
      empresaTipoCliente,
      empresaDomicilio,
      empresaLocalidad,
      empresaProvincia,
      empresaCondFrenteIva,
    } = formData;
    return !!(
      empresaRazonSocial.trim() &&
      empresaTipoCliente.trim() &&
      empresaDomicilio.trim() &&
      empresaLocalidad.trim() &&
      empresaProvincia.trim() &&
      empresaCondFrenteIva.trim()
    );
  };

  const validateTab3 = (): boolean => {
    return (
      formData.contactos.length > 0 &&
      formData.contactos[0].nombre.trim() !== "" &&
      formData.contactos[0].cargo.trim() !== "" &&
      formData.contactos[0].telefono.trim() !== "" &&
      formData.contactos[0].email.trim() !== ""
    );
  };

  const handleNextTab = () => {
    let canProceed = false;

    switch (tabValue) {
      case 0:
        canProceed = validateTab1();
        break;
      case 1:
        canProceed = validateTab2();
        break;
      case 2:
        canProceed = validateTab3();
        break;
    }

    if (canProceed) {
      if (tabValue < 2) {
        const newEnabledTabs = [...enabledTabs];
        newEnabledTabs[tabValue + 1] = true;
        setEnabledTabs(newEnabledTabs);
        setTabValue(tabValue + 1);
      }
    } else {
      alert(
        "Por favor complete todos los campos requeridos antes de continuar."
      );
    }
  };

  const handlePreviousTab = () => {
    if (tabValue > 0) {
      setTabValue(tabValue - 1);
    }
  };

  return (
    <Dialog
      open={open}
     // onClose={onClose}
      maxWidth="lg"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "1100px",
          maxWidth: "95vw",
          minHeight: "600px",
        },
      }}
    >
      <Box sx={{ p: 4 }}>
        <Box sx={{ mb: 2 }}>
          <DialogTitle
            sx={{
              p: 0,
              fontFamily: "Lexend, sans-serif",
              fontSize: "1.5rem",
              fontWeight: "bold",
              color: "#333",
            }}
          >
            {estadoModal === "add"
              ? "Registrar nuevo agricultor/ganadero"
              : "Editar agricultor/ganadero"}
          </DialogTitle>
          <DialogContentText
            sx={{
              p: 0,
              mt: 1,
              fontFamily: "Inter, sans-serif",
              color: "#666",
            }}
          >
            Complete la información del agricultor/ganadero.
          </DialogContentText>
        </Box>
        <IconButton
          aria-label="close"
          onClick={(event) => onClose(event, "closeButtonClick")}
          sx={{ position: "absolute", right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
        <Box component="form" onSubmit={handleSubmit}>
          <DialogContent sx={{ p: 0 }}>
            <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 3 }}>
              <Tabs
                value={tabValue}
                onChange={(_, newValue) => {
                  if (enabledTabs[newValue]) {
                    setTabValue(newValue);
                  }
                }}
              >
                <Tab
                  label="Propietario/Persona Física"
                  disabled={!enabledTabs[0]}
                />
                <Tab label="Empresa/Razón Social" disabled={!enabledTabs[1]} />
                <Tab label="Contacto/Encargado" disabled={!enabledTabs[2]} />
              </Tabs>
            </Box>

            {/* Tab content would go here - simplified for performance */}
            {tabValue === 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontFamily: "Lexend, sans-serif",
                    color: "#333",
                  }}
                >
                  Datos del Propietario/Persona Física
                </Typography>
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Nombre Completo *
                    </Typography>
                    <TextField
                      placeholder="Ej: Juan Carlos Pérez"
                      name="propietarioNombre"
                      fullWidth
                      value={formData.propietarioNombre}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.propietarioNombre)}
                      helperText={validationErrors.propietarioNombre}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioNombre && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Documento *
                    </Typography>
                    <TextField
                      placeholder="Ej: 20-12345678-9"
                      name="propietarioDocumento"
                      fullWidth
                      value={formData.propietarioDocumento}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.propietarioDocumento)}
                      helperText={validationErrors.propietarioDocumento}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioDocumento && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Teléfono *
                    </Typography>
                    <TextField
                      placeholder="Ej: 0341-123456"
                      name="propietarioTelefono"
                      fullWidth
                      value={formData.propietarioTelefono}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.propietarioTelefono)}
                      helperText={validationErrors.propietarioTelefono}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioTelefono && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Email *
                    </Typography>
                    <TextField
                      placeholder="Ej: <EMAIL>"
                      name="propietarioEmail"
                      type="email"
                      fullWidth
                      value={formData.propietarioEmail}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.propietarioEmail)}
                      helperText={validationErrors.propietarioEmail}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.propietarioEmail && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                </Grid>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mt: 3,
                    pt: 2,
                    borderTop: "1px solid #e0e0e0",
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={(event) => onClose(event, "closeButtonClick")}
                  >
                    Cancelar
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNextTab}
                    sx={{
                      bgcolor: "#2E7D32",
                      "&:hover": { bgcolor: "#1B5E20" },
                    }}
                  >
                    Siguiente
                  </Button>
                </Box>
              </Box>
            )}

            {/* Pestaña 2: Empresa/Razón Social */}
            {tabValue === 1 && (
              <Box sx={{ mt: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontFamily: "Lexend, sans-serif",
                    color: "#333",
                  }}
                >
                  Datos de la Empresa/Razón Social
                </Typography>
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Razón Social *
                    </Typography>
                    <TextField
                      placeholder="Ej: Agropecuaria San Juan S.A."
                      name="empresaRazonSocial"
                      fullWidth
                      value={formData.empresaRazonSocial}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.empresaRazonSocial)}
                      helperText={validationErrors.empresaRazonSocial}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.empresaRazonSocial && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Tipo de Cliente *
                    </Typography>
                    <FormControl
                      fullWidth
                      error={Boolean(validationErrors.empresaTipoCliente)}
                    >
                      <Select
                        name="empresaTipoCliente"
                        value={formData.empresaTipoCliente}
                        onChange={(e) => handleInputChange(e as any)}
                        displayEmpty
                      >
                        <MenuItem value="">
                          <em>Seleccione un tipo de cliente</em>
                        </MenuItem>
                        {tipoClienteOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.value}
                          </MenuItem>
                        ))}
                      </Select>
                      {validationErrors.empresaTipoCliente && (
                        <FormHelperText>
                          {validationErrors.empresaTipoCliente}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Domicilio *
                    </Typography>
                    <TextField
                      placeholder="Ej: Av. San Martín 1234"
                      name="empresaDomicilio"
                      fullWidth
                      value={formData.empresaDomicilio}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.empresaDomicilio)}
                      helperText={validationErrors.empresaDomicilio}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.empresaDomicilio && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Localidad *
                    </Typography>
                    <TextField
                      placeholder="Ej: Rosario"
                      name="empresaLocalidad"
                      fullWidth
                      value={formData.empresaLocalidad}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.empresaLocalidad)}
                      helperText={validationErrors.empresaLocalidad}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.empresaLocalidad && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Provincia *
                    </Typography>
                    <FormControl
                      fullWidth
                      error={Boolean(validationErrors.empresaProvincia)}
                    >
                      <Select
                        name="empresaProvincia"
                        value={formData.empresaProvincia}
                        onChange={(e) => handleInputChange(e as any)}
                        displayEmpty
                      >
                        <MenuItem value="">
                          <em>Seleccione una provincia</em>
                        </MenuItem>
                        {provincias.map((provincia) => (
                          <MenuItem key={provincia} value={provincia}>
                            {provincia}
                          </MenuItem>
                        ))}
                      </Select>
                      {validationErrors.empresaProvincia && (
                        <FormHelperText>
                          {validationErrors.empresaProvincia}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Condición frente al IVA *
                    </Typography>
                    <FormControl
                      fullWidth
                      error={Boolean(validationErrors.empresaCondFrenteIva)}
                    >
                      <Select
                        name="empresaCondFrenteIva"
                        value={formData.empresaCondFrenteIva}
                        onChange={(e) => handleInputChange(e as any)}
                        displayEmpty
                      >
                        <MenuItem value="">
                          <em>Seleccione una condición</em>
                        </MenuItem>
                        {condFrenteIvaOptions.map((condicion) => (
                          <MenuItem key={condicion} value={condicion}>
                            {condicion}
                          </MenuItem>
                        ))}
                      </Select>
                      {validationErrors.empresaCondFrenteIva && (
                        <FormHelperText>
                          {validationErrors.empresaCondFrenteIva}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mt: 3,
                    pt: 2,
                    borderTop: "1px solid #e0e0e0",
                  }}
                >
                  <Button variant="outlined" onClick={handlePreviousTab}>
                    Anterior
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNextTab}
                    sx={{
                      bgcolor: "#2E7D32",
                      "&:hover": { bgcolor: "#1B5E20" },
                    }}
                  >
                    Siguiente
                  </Button>
                </Box>
              </Box>
            )}

            {/* Pestaña 3: Contacto/Encargado */}
            {tabValue === 2 && (
              <Box sx={{ mt: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontFamily: "Lexend, sans-serif",
                    color: "#333",
                  }}
                >
                  Datos del Contacto/Encargado Principal
                </Typography>
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Nombre del Contacto *
                    </Typography>
                    <TextField
                      placeholder="Ej: María González"
                      name="contactos[0].nombre"
                      fullWidth
                      value={formData.contactos[0]?.nombre || ""}
                      onChange={(e) => {
                        const newContactos = [...formData.contactos];
                        newContactos[0] = {
                          ...newContactos[0],
                          nombre: e.target.value,
                        };
                        setFormData({ ...formData, contactos: newContactos });
                      }}
                      error={Boolean(validationErrors["contactos[0].nombre"])}
                      helperText={validationErrors["contactos[0].nombre"]}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.contactos[0]?.nombre && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Cargo *
                    </Typography>
                    <TextField
                      placeholder="Ej: Gerente General"
                      name="contactos[0].cargo"
                      fullWidth
                      value={formData.contactos[0]?.cargo || ""}
                      onChange={(e) => {
                        const newContactos = [...formData.contactos];
                        newContactos[0] = {
                          ...newContactos[0],
                          cargo: e.target.value,
                        };
                        setFormData({ ...formData, contactos: newContactos });
                      }}
                      error={Boolean(validationErrors["contactos[0].cargo"])}
                      helperText={validationErrors["contactos[0].cargo"]}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.contactos[0]?.cargo && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Teléfono *
                    </Typography>
                    <TextField
                      placeholder="Ej: 0341-123456"
                      name="contactos[0].telefono"
                      fullWidth
                      value={formData.contactos[0]?.telefono || ""}
                      onChange={(e) => {
                        const newContactos = [...formData.contactos];
                        newContactos[0] = {
                          ...newContactos[0],
                          telefono: e.target.value,
                        };
                        setFormData({ ...formData, contactos: newContactos });
                      }}
                      error={Boolean(validationErrors["contactos[0].telefono"])}
                      helperText={validationErrors["contactos[0].telefono"]}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.contactos[0]?.telefono && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="body2" sx={labelStyles}>
                      Email *
                    </Typography>
                    <TextField
                      placeholder="Ej: <EMAIL>"
                      name="contactos[0].email"
                      type="email"
                      fullWidth
                      value={formData.contactos[0]?.email || ""}
                      onChange={(e) => {
                        const newContactos = [...formData.contactos];
                        newContactos[0] = {
                          ...newContactos[0],
                          email: e.target.value,
                        };
                        setFormData({ ...formData, contactos: newContactos });
                      }}
                      error={Boolean(validationErrors["contactos[0].email"])}
                      helperText={validationErrors["contactos[0].email"]}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.contactos[0]?.email && (
                                <CheckCircleIcon
                                  sx={{ color: "green", fontSize: 20 }}
                                />
                              )}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Grid>
                </Grid>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mt: 3,
                    pt: 2,
                    borderTop: "1px solid #e0e0e0",
                  }}
                >
                  <Button variant="outlined" onClick={handlePreviousTab}>
                    Anterior
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    sx={{
                      bgcolor: "#2E7D32",
                      "&:hover": { bgcolor: "#1B5E20" },
                    }}
                  >
                    {estadoModal === "add"
                      ? "Registrar Agricultor"
                      : "Actualizar Agricultor"}
                  </Button>
                </Box>
              </Box>
            )}
          </DialogContent>
        </Box>
      </Box>
    </Dialog>
  );
};

export default AgricultorModal;
